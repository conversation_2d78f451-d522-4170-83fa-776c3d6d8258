# Studio-Agentique-Roony

🤖 **Roony** - Studio Agentique de Workflow IA

## Description

Roony est un studio agentique avancé qui guide les utilisateurs à travers un processus structuré de résolution de problèmes complexes en utilisant l'intelligence artificielle. L'application propose un workflow en 15 étapes pour analyser, innover et générer des solutions sur mesure.

## Fonctionnalités

- ✨ **Interface intuitive** avec design moderne
- 🧠 **Mascotte intelligente** qui s'affiche pendant les phases de raisonnement
- 📊 **Suivi de progression** en temps réel
- 🔄 **Workflow structuré** en 15 étapes
- 🎯 **Génération de prompts optimisés**
- 📱 **Footer slide interactif** avec liens sociaux

## Technologies utilisées

- **React 19** + **TypeScript**
- **Vite.js** pour le développement et le build
- **Tailwind CSS** pour le styling
- **GSAP** pour les animations
- **Google Gemini AI** pour l'intelligence artificielle

## Installation

```bash
# Cloner le repository
git clone https://github.com/cisco-03/Studio-Agentique-Roony.git

# Installer les dépendances
npm install

# Lancer en mode développement
npm run dev
```

## Configuration

Créer un fichier `.env.local` avec vos clés API :

```env
VITE_GEMINI_API_KEY=votre_clé_api_gemini
```

## 🚀 Configuration Requise

Pour fonctionner, cette application **doit** avoir accès à une clé API OpenRouter via une variable d'environnement.

### 1. Obtenir une Clé API OpenRouter

- Rendez-vous sur [OpenRouter.ai](https://openrouter.ai/).
- Créez un compte ou connectez-vous.
- Accédez à votre page de clés (Keys) pour en générer une nouvelle.

### 2. Configuration de l'Environnement

L'application est conçue pour récupérer la clé API directement de l'environnement d'exécution. Vous devez configurer une variable d'environnement nommée `API_KEY` avec votre clé OpenRouter.

---

## 🤖 Fonctionnement de la Sélection de Modèles via OpenRouter

L'un des points forts de cette application est sa capacité à choisir le meilleur outil pour chaque tâche. Voici comment cela fonctionne :

1.  **Une Seule Clé, Plusieurs Modèles** : Votre unique clé API OpenRouter nous donne accès à des dizaines de modèles d'IA différents.

2.  **Tâches Catégorisées** : Chaque étape de notre workflow (défini dans `constants.ts`) est associée à un type de tâche spécifique : `'analyse'`, `'génération'`, `'validation'`, ou `'synthèse'`.

3.  **Sélection Intelligente** :
    - Le fichier `constants.ts` contient un objet `OPENROUTER_MODELS` qui liste les modèles gratuits les plus performants pour chaque type de tâche.
    - Lorsque nous arrivons à une nouvelle étape, le service (`services/geminiService.ts`) consulte cette liste et choisit aléatoirement un des modèles appropriés pour la tâche en cours.

4.  **Transparence Totale** : Pour que vous sachiez toujours quel "cerveau" est à l'œuvre, l'agent IA annonce ses choix dans l' "Espace de Raisonnement".

---

## 🔌 Extensibilité : Connexion aux Données (Vision Future)

L'application est conçue pour être extensible. À l'avenir, l'agent pourra se connecter à des sources de données externes (bases de données, API, etc.) pour enrichir son analyse.

### Architecture en Place

-   **Configuration Centralisée** : Le fichier `constants.ts` contient une liste `DATA_SOURCES`. C'est ici que vous pouvez déclarer de nouvelles sources de données.
-   **Service Dédié** : Le fichier `services/dataSourceService.ts` contient la logique pour interroger ces sources. Actuellement, il s'agit d'une simulation, mais il est prêt à être connecté à de vraies API.

### Comment Ajouter une Nouvelle Source de Données ?

1.  **Déclarez la source** dans `DATA_SOURCES` dans `constants.ts`.
2.  **Implémentez la logique d'appel** dans `services/dataSourceService.ts`.

L'agent pourra alors être instruit de "rechercher des informations dans MCP Server Context7" et utilisera ce service pour obtenir des données en temps réel.

## Déploiement

L'application est optimisée pour le déploiement sur Netlify.

## Auteur

**FlexoDiv** - Ingénieur en Prompting et Développement IA

- 🌐 [Portfolio](https://flexodiv.netlify.app/)
- 📧 [Email](mailto:<EMAIL>)
- 💼 [LinkedIn](https://www.linkedin.com/in/flexodiv-engineering-prompting-982582203)
- 🎥 [YouTube](https://www.youtube.com/@flexodiv)

## Licence

© 2025 FlexoDiv - Tous droits réservés